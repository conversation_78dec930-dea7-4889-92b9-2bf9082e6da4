<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>State → Goal → Action System</title>

    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 240 10% 3.9%;
            --card: 0 0% 100%;
            --card-foreground: 240 10% 3.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 240 10% 3.9%;
            --primary: 240 9% 9%;
            --primary-foreground: 0 0% 98%;
            --secondary: 240 4.8% 95.9%;
            --secondary-foreground: 240 5.9% 10%;
            --muted: 240 4.8% 95.9%;
            --muted-foreground: 240 3.8% 46.1%;
            --accent: 240 4.8% 95.9%;
            --accent-foreground: 240 5.9% 10%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 0 0% 98%;
            --border: 240 5.9% 90%;
            --input: 240 5.9% 90%;
            --ring: 240 10% 3.9%;
            --radius: 0.5rem;
        }

        [data-theme="dark"] {
            --background: 240 10% 3.9%;
            --foreground: 0 0% 98%;
            --card: 240 10% 3.9%;
            --card-foreground: 0 0% 98%;
            --popover: 240 10% 3.9%;
            --popover-foreground: 0 0% 98%;
            --primary: 0 0% 98%;
            --primary-foreground: 240 5.9% 10%;
            --secondary: 240 3.7% 15.9%;
            --secondary-foreground: 0 0% 98%;
            --muted: 240 3.7% 15.9%;
            --muted-foreground: 240 5% 64.9%;
            --accent: 240 3.7% 15.9%;
            --accent-foreground: 0 0% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 0 0% 98%;
            --border: 240 3.7% 15.9%;
            --input: 240 3.7% 15.9%;
            --ring: 240 4.9% 83.9%;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: hsl(var(--background));
            color: hsl(var(--foreground));
            line-height: 1.5;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid hsl(var(--border));
        }

        .title {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary)) 60%, hsl(var(--muted-foreground)));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .theme-toggle {
            background: hsl(var(--secondary));
            border: 1px solid hsl(var(--border));
            color: hsl(var(--foreground));
            padding: 0.5rem;
            border-radius: var(--radius);
            cursor: pointer;
            transition: all 0.2s;
        }

        .theme-toggle:hover {
            background: hsl(var(--accent));
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .card {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: calc(var(--radius) + 2px);
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: hsl(var(--foreground));
        }

        .input, .select, .textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
            font-size: 0.875rem;
            transition: border-color 0.2s;
        }

        .input:focus, .select:focus, .textarea:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .textarea {
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius);
            cursor: pointer;
            font-weight: 500;
            transition: opacity 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn:hover {
            opacity: 0.9;
        }

        .btn-secondary {
            background: hsl(var(--secondary));
            color: hsl(var(--secondary-foreground));
        }

        .matrix-container {
            margin-top: 2rem;
        }

        .matrix-table {
            width: 100%;
            border-collapse: collapse;
            background: hsl(var(--card));
            border-radius: var(--radius);
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .matrix-table th,
        .matrix-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid hsl(var(--border));
        }

        .matrix-table th {
            background: hsl(var(--secondary));
            font-weight: 600;
            color: hsl(var(--secondary-foreground));
        }

        .matrix-table tr:hover {
            background: hsl(var(--accent));
        }

        .actions-list {
            list-style: none;
            padding-left: 0;
        }

        .actions-list li {
            padding: 0.25rem 0;
            position: relative;
            padding-left: 1.5rem;
        }

        .actions-list li:before {
            content: "→";
            position: absolute;
            left: 0;
            color: hsl(var(--primary));
            font-weight: bold;
        }

        .framework-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: hsl(var(--secondary));
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--primary)) 70%, hsl(var(--muted-foreground)));
            transition: width 0.3s ease;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .stat-card {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            padding: 1.5rem;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: hsl(var(--primary));
        }

        .stat-label {
            font-size: 0.875rem;
            color: hsl(var(--muted-foreground));
            margin-top: 0.5rem;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: hsl(var(--muted-foreground));
        }

        .empty-state h3 {
            margin-bottom: 1rem;
            color: hsl(var(--foreground));
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body data-theme="dark">
    <div class="container">
        <div class="header">
            <h1 class="title">State → Goal → Action System</h1>
            <button class="theme-toggle" onclick="toggleTheme()">
                <span id="theme-icon">☀️</span>
            </button>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="total-entries">0</div>
                <div class="stat-label">Total Mappings</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="frameworks-used">0</div>
                <div class="stat-label">Frameworks Used</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="actions-count">0</div>
                <div class="stat-label">Total Actions</div>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h2 class="card-title">
                    🎯 Define State → Goal Mapping
                </h2>
                <form id="mapping-form">
                    <div class="input-group">
                        <label class="label">Current State</label>
                        <textarea class="textarea" id="current-state" placeholder="e.g., Low motivation, scattered focus, unclear priorities"></textarea>
                    </div>
                    
                    <div class="input-group">
                        <label class="label">Goal State</label>
                        <textarea class="textarea" id="goal-state" placeholder="e.g., Consistent daily output, clear focus, structured workflow"></textarea>
                    </div>
                    
                    <div class="input-group">
                        <label class="label">Framework/Tool</label>
                        <select class="select" id="framework">
                            <option value="GROW">GROW (Goal-Reality-Options-Will)</option>
                            <option value="SMART">SMART Goals</option>
                            <option value="SWOT">SWOT Analysis</option>
                            <option value="OKR">OKRs (Objectives & Key Results)</option>
                            <option value="GTD">Getting Things Done</option>
                            <option value="STAGES">Stages of Change</option>
                            <option value="KANBAN">Kanban/Visual Flow</option>
                            <option value="CUSTOM">Custom Approach</option>
                        </select>
                    </div>
                    
                    <div class="input-group">
                        <label class="label">Priority Actions (one per line)</label>
                        <textarea class="textarea" id="actions" placeholder="1. Set 3 daily non-negotiables&#10;2. Block calendar for deep work&#10;3. Remove 2 biggest distractions"></textarea>
                    </div>
                    
                    <button type="submit" class="btn">
                        ➕ Add Mapping
                    </button>
                </form>
            </div>

            <div class="card">
                <h2 class="card-title">
                    🧠 AI Planning Assistant
                </h2>
                <div class="input-group">
                    <label class="label">Describe Your Challenge</label>
                    <textarea class="textarea" id="ai-input" placeholder="e.g., I need to launch a product but keep getting distracted by perfectionism"></textarea>
                </div>
                <button class="btn btn-secondary" onclick="generateSuggestions()">
                    🪄 Generate Action Plan
                </button>
                <div id="ai-suggestions" style="margin-top: 1rem;"></div>
            </div>
        </div>

        <div class="matrix-container">
            <h2 class="card-title">
                📊 State-Goal-Action Matrix
            </h2>
            <div id="matrix-content">
                <div class="empty-state">
                    <h3>No mappings yet</h3>
                    <p>Add your first state → goal mapping above to see your action matrix</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let mappings = [];
        
        // Initialize icons - no longer needed
        // lucide.createIcons();
        
        // Theme toggle
        function toggleTheme() {
            const body = document.body;
            const icon = document.getElementById('theme-icon');
            
            if (body.getAttribute('data-theme') === 'dark') {
                body.setAttribute('data-theme', 'light');
                icon.textContent = '🌙';
            } else {
                body.setAttribute('data-theme', 'dark');
                icon.textContent = '☀️';
            }
        }
        
        // Form submission
        document.getElementById('mapping-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const currentState = document.getElementById('current-state').value.trim();
            const goalState = document.getElementById('goal-state').value.trim();
            const framework = document.getElementById('framework').value;
            const actions = document.getElementById('actions').value.trim();
            
            if (!currentState || !goalState || !actions) {
                alert('Please fill in all required fields');
                return;
            }
            
            const mapping = {
                id: Date.now(),
                currentState,
                goalState,
                framework,
                actions: actions.split('\n').filter(a => a.trim()),
                created: new Date().toLocaleDateString()
            };
            
            mappings.push(mapping);
            updateMatrix();
            updateStats();
            
            // Clear form
            document.getElementById('mapping-form').reset();
        });
        
        // Generate AI suggestions
        function generateSuggestions() {
            const input = document.getElementById('ai-input').value.trim();
            const suggestionsDiv = document.getElementById('ai-suggestions');
            
            if (!input) {
                suggestionsDiv.innerHTML = '<p style="color: hsl(var(--muted-foreground));">Please describe your challenge first</p>';
                return;
            }
            
            // Simulate AI analysis with common patterns
            const suggestions = analyzeChallenge(input);
            
            suggestionsDiv.innerHTML = `
                <div style="background: hsl(var(--secondary)); padding: 1rem; border-radius: var(--radius); margin-top: 1rem;">
                    <h4 style="margin-bottom: 0.5rem; color: hsl(var(--foreground));">Suggested Framework: ${suggestions.framework}</h4>
                    <p style="color: hsl(var(--muted-foreground)); font-size: 0.875rem; margin-bottom: 1rem;">${suggestions.reasoning}</p>
                    <div style="margin-bottom: 1rem;">
                        <strong>Recommended Actions:</strong>
                        <ul class="actions-list" style="margin-top: 0.5rem;">
                            ${suggestions.actions.map(action => `<li>${action}</li>`).join('')}
                        </ul>
                    </div>
                    <button class="btn btn-secondary" onclick="fillFromSuggestion('${suggestions.framework}', '${suggestions.actions.join('\\n')}')" style="font-size: 0.875rem; padding: 0.5rem 1rem;">
                        📋 Use This Plan
                    </button>
                </div>
            `;
            lucide.createIcons();
        }
        
        function analyzeChallenge(input) {
            const lower = input.toLowerCase();
            
            // Pattern matching for common challenges
            if (lower.includes('perfect') || lower.includes('overwhelm')) {
                return {
                    framework: 'STAGES',
                    reasoning: 'Perfectionism suggests behavioral change is needed. Focus on small wins first.',
                    actions: [
                        'Set "good enough" standards for 80% of tasks',
                        'Time-box work sessions (25 min max)',
                        'Ship one imperfect thing daily'
                    ]
                };
            } else if (lower.includes('distract') || lower.includes('focus')) {
                return {
                    framework: 'GTD',
                    reasoning: 'Distraction issues need systematic capture and prioritization systems.',
                    actions: [
                        'Brain dump all open loops into one list',
                        'Block 2-hour focus sessions daily',
                        'Remove phone from workspace'
                    ]
                };
            } else if (lower.includes('goal') || lower.includes('unclear')) {
                return {
                    framework: 'OKR',
                    reasoning: 'Unclear goals need specific, measurable outcomes with key results.',
                    actions: [
                        'Define one main objective for next 90 days',
                        'Set 3 measurable key results',
                        'Weekly progress check-ins'
                    ]
                };
            } else {
                return {
                    framework: 'GROW',
                    reasoning: 'General challenges benefit from structured goal-setting and reality assessment.',
                    actions: [
                        'Write current reality assessment',
                        'Define specific desired outcome',
                        'List 3 immediate next actions'
                    ]
                };
            }
        }
        
        function fillFromSuggestion(framework, actions) {
            document.getElementById('framework').value = framework;
            document.getElementById('actions').value = actions;
        }
        
        // Update matrix display
        function updateMatrix() {
            const matrixContent = document.getElementById('matrix-content');
            
            if (mappings.length === 0) {
                matrixContent.innerHTML = `
                    <div class="empty-state">
                        <h3>No mappings yet</h3>
                        <p>Add your first state → goal mapping above to see your action matrix</p>
                    </div>
                `;
                return;
            }
            
            const tableHTML = `
                <table class="matrix-table">
                    <thead>
                        <tr>
                            <th>Current State</th>
                            <th>Goal State</th>
                            <th>Framework</th>
                            <th>Priority Actions</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${mappings.map(mapping => `
                            <tr>
                                <td>${mapping.currentState}</td>
                                <td>${mapping.goalState}</td>
                                <td><span class="framework-badge">${mapping.framework}</span></td>
                                <td>
                                    <ul class="actions-list">
                                        ${mapping.actions.map(action => `<li>${action}</li>`).join('')}
                                    </ul>
                                </td>
                                <td>
                                    <button class="btn btn-secondary" onclick="deleteMapping(${mapping.id})" style="font-size: 0.75rem; padding: 0.5rem;">
                                        🗑️ Delete
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            matrixContent.innerHTML = tableHTML;
            lucide.createIcons();
        }
        
        // Update statistics
        function updateStats() {
            document.getElementById('total-entries').textContent = mappings.length;
            
            const uniqueFrameworks = [...new Set(mappings.map(m => m.framework))];
            document.getElementById('frameworks-used').textContent = uniqueFrameworks.length;
            
            const totalActions = mappings.reduce((sum, m) => sum + m.actions.length, 0);
            document.getElementById('actions-count').textContent = totalActions;
        }
        
        // Delete mapping
        function deleteMapping(id) {
            mappings = mappings.filter(m => m.id !== id);
            updateMatrix();
            updateStats();
        }
        
        // Initialize
        updateMatrix();
        updateStats();
    </script>
</body>
</html>